package org.example;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.Enumeration;

public class Main {
    public static void main(String[] args) {
        System.out.println("Hello world!");
    }

    /**
     * 获取CPU序列号
     */
    public static String getCPUSerial() {
        String os = System.getProperty("os.name").toLowerCase();
        
        // 根据操作系统类型选择不同的实现
        if (os.contains("win")) {
            return getWindowsCPUSerial();
        } else if (os.contains("linux")) {
            return getLinuxCPUSerial();
        } else {
            // 其他系统可能需要其他实现
            return null;
        }
    }

    /**
     * 获取Windows系统CPU序列号
     */
    private static String getWindowsCPUSerial() {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("CPUSerial", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery _ \n"
                    + "   (\"Select * from Win32_Processor\") \n"
                    + "For Each objItem in colItems \n"
                    + "    Wscript.Echo objItem.ProcessorId \n"
                    + "    exit for  ' do the first cpu only! \n" + "Next \n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line);
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            // logGetMachine(3, " 1"); // This line was not provided in the original file, so it's commented out.
            result = null;
        }
        if(result == null || result.toString().trim().isEmpty()) {
            return null;
        }
        return result.toString().trim();
    }

    /**
     * 获取Linux系统CPU序列号
     */
    private static String getLinuxCPUSerial() {
        String result = null;
        try {
            // Linux系统下通过读取/proc/cpuinfo文件获取CPU信息
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "cat /proc/cpuinfo | grep Serial | cut -d ':' -f 2 | tr -d ' '"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            // 如果上面的方法没有获取到，尝试通过physical id和cpu cores组合作为唯一标识
            if (line == null || line.trim().isEmpty()) {
                p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "cat /proc/cpuinfo | grep -E 'physical id|core id' | sort -u | md5sum"});
                input = new BufferedReader(new InputStreamReader(p.getInputStream()));
                line = input.readLine();
                input.close();
            }
            
            if (line != null && !line.trim().isEmpty()) {
                result = line.trim();
            }
        } catch (Exception e) {
            // logGetMachine(3, " 2"); // This line was not provided in the original file, so it's commented out.
        }
        return result;
    }

    /**
     * 获取硬盘序列号
     *
     * @param drive 盘符或设备名称
     */
    public static String getHardDiskSN(String drive) {
        String os = System.getProperty("os.name").toLowerCase();
        
        // 根据操作系统类型选择不同的实现
        if (os.contains("win")) {
            return getWindowsHardDiskSN(drive);
        } else if (os.contains("linux")) {
            return getLinuxHardDiskSN(drive);
        } else {
            // 其他系统可能需要其他实现
            return null;
        }
    }
    
    /**
     * 获取Windows系统硬盘序列号
     *
     * @param drive 盘符，例如 "C:"
     */
    private static String getWindowsHardDiskSN(String drive) {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("disksn", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new java.io.FileWriter(file);
            String vbs = "Set objFSO = CreateObject(\"Scripting.FileSystemObject\")\n"
                    + "Set colDrives = objFSO.Drives\n"
                    + "Set objDrive = colDrives.item(\""
                    + drive
                    + "\")\n"
                    + "Wscript.Echo objDrive.SerialNumber"; // see note
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line);
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            // logGetMachine(3, " 2");
            result = null;
        }
        if(result != null) {
            return result.toString().trim();
        }
        return null;
    }
    
    /**
     * 获取Linux系统硬盘序列号
     *
     * @param device 设备名称，例如 "sda"、"nvme0n1"，如果为null或空则获取系统盘
     */
    private static String getLinuxHardDiskSN(String device) {
        String devicePath = device;
        
        // 如果没有指定设备，尝试获取系统盘(根分区所在的设备)
        if (devicePath == null || devicePath.trim().isEmpty()) {
            devicePath = getLinuxSystemDisk();
            if (devicePath == null) {
                // 如果无法确定系统盘，使用默认值
                devicePath = "sda";
            }
        }
        
        // 如果没有以/dev/开头，则添加
        if (!devicePath.startsWith("/dev/")) {
            devicePath = "/dev/" + devicePath;
        }
        
        String result = null;
        
        try {
            // 尝试方法1: 使用 lsblk 命令
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "lsblk -d -o NAME,SERIAL | grep " + devicePath.substring(devicePath.lastIndexOf("/") + 1)});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                // 解析lsblk输出获取序列号部分
                String[] parts = line.trim().split("\\s+");
                if (parts.length > 1) {
                    return parts[1].trim();
                }
            }
            
            // 尝试方法2: 使用 hdparm 命令
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "hdparm -i " + devicePath + " | grep SerialNo"});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && line.contains("SerialNo=")) {
                // 解析hdparm输出获取序列号部分
                int index = line.indexOf("SerialNo=");
                if (index != -1) {
                    String serialPart = line.substring(index + 9);
                    // 提取序列号直到下一个空格或结束
                    int endIndex = serialPart.indexOf(" ");
                    if (endIndex != -1) {
                        return serialPart.substring(0, endIndex).trim();
                    } else {
                        return serialPart.trim();
                    }
                }
            }
            
            // 尝试方法3: 使用 smartctl 命令 (需要安装 smartmontools)
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "smartctl -i " + devicePath + " | grep 'Serial Number'"});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                // 解析smartctl输出获取序列号部分
                String[] parts = line.split(":");
                if (parts.length > 1) {
                    return parts[1].trim();
                }
            }
            
        } catch (Exception e) {
            // logGetMachine(3, " 3");
        }
        
        return result;
    }
    
    /**
     * 获取Linux系统盘(根分区所在的设备)
     * 
     * @return 系统盘设备名称（如"sda"、"nvme0n1"等），如果无法确定则返回null
     */
    private static String getLinuxSystemDisk() {
        String systemDisk = null;
        
        try {
            // 方法1: 使用df命令查看根分区挂载情况
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "df -h / | grep /dev/"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                // 解析df输出获取设备名
                String[] parts = line.trim().split("\\s+");
                if (parts.length > 0) {
                    // 提取设备名
                    String devicePath = parts[0].trim();
                    // 移除"/dev/"前缀并提取主设备名(例如从/dev/sda1中提取sda)
                    if (devicePath.startsWith("/dev/")) {
                        devicePath = devicePath.substring(5);
                    }
                    
                    // 移除数字部分，获取主设备名
                    systemDisk = devicePath.replaceAll("\\d+$", "");
                    return systemDisk;
                }
            }
            
            // 方法2: 使用mount命令
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "mount | grep \"on / \""});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && line.contains("/dev/")) {
                int startIndex = line.indexOf("/dev/") + 5;
                int endIndex = line.indexOf(" ", startIndex);
                if (endIndex != -1) {
                    String devicePath = line.substring(startIndex, endIndex);
                    // 移除数字部分，获取主设备名
                    systemDisk = devicePath.replaceAll("\\d+$", "");
                    return systemDisk;
                }
            }
            
            // 方法3: 使用lsblk命令查找挂载在/根目录的设备
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "lsblk -no NAME,MOUNTPOINT | grep ' /$'"});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                String[] parts = line.trim().split("\\s+");
                if (parts.length > 0) {
                    String deviceName = parts[0].trim();
                    // 移除数字部分，获取主设备名
                    systemDisk = deviceName.replaceAll("\\d+$", "");
                    return systemDisk;
                }
            }
            
        } catch (Exception e) {
            // 出现异常时返回null
        }
        
        return systemDisk;
    }

    /**
     * 获取主板序列号
     */
    public static String getMotherboardSerial() {
        String os = System.getProperty("os.name").toLowerCase();
        
        if (os.contains("win")) {
            return getWindowsMotherboardSerial();
        } else if (os.contains("linux")) {
            return getLinuxMotherboardSerial();
        } else {
            // 其他系统可能需要其他实现
            return null;
        }
    }
    
    /**
     * 获取Windows系统主板序列号
     */
    private static String getWindowsMotherboardSerial() {
        StringBuilder result = new StringBuilder();
        
        // 尝试方法1: 使用WMI获取主板序列号
        try {
            File file = File.createTempFile("motherboard", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BaseBoard\")\n"
                    + "For Each objItem in colItems\n"
                    + "    If objItem.SerialNumber <> \"\" And objItem.SerialNumber <> \"None\" And objItem.SerialNumber <> \"To be filled by O.E.M.\" Then\n"
                    + "        Wscript.Echo objItem.SerialNumber\n"
                    + "        Exit For\n"
                    + "    End If\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                if(!line.trim().isEmpty()) {
                    result.append(line.trim());
                }
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            // 忽略异常
        }
        
        // 如果方法1未获取到有效的序列号，尝试方法2：获取主板型号和制造商的组合
        if (result.toString().trim().isEmpty()) {
            try {
                File file = File.createTempFile("motherboard_model", ".vbs");
                file.deleteOnExit();
                FileWriter fw = new FileWriter(file);
                String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                        + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BaseBoard\")\n"
                        + "For Each objItem in colItems\n"
                        + "    Wscript.Echo objItem.Manufacturer & \"-\" & objItem.Product\n"
                        + "    Exit For\n"
                        + "Next\n";
                fw.write(vbs);
                fw.close();
                Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
                BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
                String line;
                while ((line = input.readLine()) != null) {
                    if(!line.trim().isEmpty() && !line.contains("To be filled by O.E.M.")) {
                        result.append(line.trim());
                    }
                }
                input.close();
                file.delete();
            } catch (Exception ex) {
                // 忽略异常
            }
        }
        
        // 如果方法2也未获取到有效信息，尝试方法3：使用BIOS UUID作为备选
        if (result.toString().trim().isEmpty()) {
            try {
                File file = File.createTempFile("bios_uuid", ".vbs");
                file.deleteOnExit();
                FileWriter fw = new FileWriter(file);
                String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                        + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_ComputerSystemProduct\")\n"
                        + "For Each objItem in colItems\n"
                        + "    Wscript.Echo objItem.UUID\n"
                        + "    Exit For\n"
                        + "Next\n";
                fw.write(vbs);
                fw.close();
                Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
                BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
                String line;
                while ((line = input.readLine()) != null) {
                    if(!line.trim().isEmpty() && !line.equals("FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF")) {
                        result.append("UUID-").append(line.trim());
                    }
                }
                input.close();
                file.delete();
            } catch (Exception ex) {
                // 忽略异常
            }
        }
        
        return !result.toString().trim().isEmpty() ? result.toString() : null;
    }
    
    /**
     * 获取Linux系统主板序列号
     */
    private static String getLinuxMotherboardSerial() {
        String result = null;
        
        // 尝试方法1: 使用dmidecode命令获取主板序列号
        try {
            // 注意: dmidecode通常需要root权限
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "sudo dmidecode -t baseboard | grep 'Serial Number' | cut -d ':' -f2"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && !line.contains("Not Specified") && !line.contains("To be filled")) {
                result = line.trim();
                return result;
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一个方法
        }
        
        // 尝试方法2: 获取主板型号和制造商
        try {
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                "echo $(sudo dmidecode -t baseboard | grep 'Manufacturer' | cut -d ':' -f2)-$(sudo dmidecode -t baseboard | grep 'Product Name' | cut -d ':' -f2)"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && !line.contains("Not Specified") && !line.contains("To be filled") && !line.equals("-")) {
                result = line.trim();
                return result;
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一个方法
        }
        
        // 尝试方法3: 无权限时使用/sys/class/dmi/id下的信息
        try {
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "cat /sys/class/dmi/id/board_serial"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && !line.contains("Not Specified") && !line.contains("To be filled")) {
                result = line.trim();
                return result;
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一个方法
        }
        
        // 尝试方法4: 使用系统UUID作为备选
        try {
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "cat /sys/class/dmi/id/product_uuid"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && !line.contains("Not Specified") && !line.contains("To be filled") && 
                !line.equals("00000000-0000-0000-0000-000000000000") && !line.equals("FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF")) {
                result = "UUID-" + line.trim();
                return result;
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return result;
    }

    /**
     * 获取BIOS信息
     */
    public static String getBIOSInfo() {
        String os = System.getProperty("os.name").toLowerCase();
        
        if (os.contains("win")) {
            return getWindowsBIOSInfo();
        } else if (os.contains("linux")) {
            return getLinuxBIOSInfo();
        } else {
            // 其他系统可能需要其他实现
            return null;
        }
    }
    
    /**
     * 获取Windows系统BIOS信息
     */
    private static String getWindowsBIOSInfo() {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("bios", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BIOS\")\n"
                    + "For Each objItem in colItems\n"
                    + "    Wscript.Echo objItem.Manufacturer & \"|\" & objItem.SerialNumber & \"|\" & objItem.Version & \"|\" & objItem.ReleaseDate\n"
                    + "    Exit For\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line.trim());
            }
            input.close();
            file.delete();
            
            // 如果获取的序列号是常见的占位符，尝试使用UUID
            if (result.toString().contains("To be filled by O.E.M.") || result.toString().contains("Default")) {
                return getWindowsSystemUUID();
            }
        } catch (Exception e) {
            // 如果上面的方法失败，尝试只获取BIOS序列号
            try {
                File file = File.createTempFile("bios_serial", ".vbs");
                file.deleteOnExit();
                FileWriter fw = new FileWriter(file);
                String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                        + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BIOS\")\n"
                        + "For Each objItem in colItems\n"
                        + "    Wscript.Echo objItem.SerialNumber\n"
                        + "    Exit For\n"
                        + "Next\n";
                fw.write(vbs);
                fw.close();
                Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
                BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
                String line;
                while ((line = input.readLine()) != null) {
                    result.append(line.trim());
                }
                input.close();
                file.delete();
            } catch (Exception ex) {
                // 如果还是失败，尝试获取系统UUID
                return getWindowsSystemUUID();
            }
        }
        
        return !result.toString().trim().isEmpty() ? result.toString() : getWindowsSystemUUID();
    }
    
    /**
     * 获取Windows系统UUID
     */
    private static String getWindowsSystemUUID() {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("system_uuid", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_ComputerSystemProduct\")\n"
                    + "For Each objItem in colItems\n"
                    + "    Wscript.Echo \"UUID-\" & objItem.UUID\n"
                    + "    Exit For\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                if (!line.trim().isEmpty() && !line.contains("FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF")) {
                    result.append(line.trim());
                }
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            // 忽略异常
        }
        return !result.toString().trim().isEmpty() ? result.toString() : null;
    }
    
    /**
     * 获取Linux系统BIOS信息
     */
    private static String getLinuxBIOSInfo() {
        String result = null;
        
        // 尝试方法1: 使用dmidecode获取BIOS信息（需要root权限）
        try {
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "sudo dmidecode -t bios | grep -E 'Vendor|Serial Number|Version|Release Date' | awk -F': ' '{print $2}' | paste -sd '|'"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && !line.contains("Not Specified") && !line.contains("To be filled")) {
                return line.trim();
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一个方法
        }
        
        // 尝试方法2: 从/sys/class/dmi/id目录读取BIOS信息（普通用户可能有权限）
        try {
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "echo $(cat /sys/class/dmi/id/bios_vendor)|$(cat /sys/class/dmi/id/bios_version)|$(cat /sys/class/dmi/id/bios_date)"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && !line.equals("||")) {
                return line.trim();
            }
        } catch (Exception e) {
            // 忽略异常，尝试下一个方法
        }
        
        // 尝试方法3: 获取系统UUID作为备选
        try {
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "cat /sys/class/dmi/id/product_uuid"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty() && 
                !line.equals("00000000-0000-0000-0000-000000000000") && 
                !line.equals("FFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF")) {
                return "UUID-" + line.trim();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return result;
    }

    /*
     * 取机器的mac地址和本机IP地址
     * ipAddress = true 取IP地址，false 取MAC地址
     */
    public static String getMacAddress(boolean ipAddress) {
        String os = System.getProperty("os.name").toLowerCase();
        
        if (os.contains("linux")) {
            return getLinuxMacAddress(ipAddress);
        } else {
            // Windows和其他系统使用原有实现
            return getGenericMacAddress(ipAddress);
        }
    }
    
    /*
     * 通用方法获取机器的MAC地址和IP地址（适用于Windows和大多数系统）
     * ipAddress = true 取IP地址，false 取MAC地址
     */
    private static String getGenericMacAddress(boolean ipAddress) {
        String macs = null;
        String localip = null;
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress inetAddress;
            boolean finded = false; // 是否找到外网IP
            while (netInterfaces.hasMoreElements() && !finded) {
                NetworkInterface ni = netInterfaces.nextElement();
                Enumeration<InetAddress> address = ni.getInetAddresses();
                while (address.hasMoreElements()) {
                    inetAddress = address.nextElement();
                    String ip = inetAddress.getHostAddress();
                    if (ip.contains(":") || ip.startsWith("221.231.") || ip.equalsIgnoreCase("127.0.0.1")) {
                        //过滤这些网址
                        continue;
                    }
                    //System.out.println(ni.getName() + " - " + inetAddress.getHostAddress() + " - " + inetAddress.isSiteLocalAddress() + " - " + !inetAddress.isLoopbackAddress());
                    if (!inetAddress.isSiteLocalAddress() && !inetAddress.isLoopbackAddress()) { //外网
                        localip = inetAddress.getHostAddress();
                        byte[] mac = ni.getHardwareAddress();
                        if (mac == null) {
                            continue;
                        }
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < mac.length; i++) {
                            if (i != 0) {
                                sb.append("-");
                            }
                            String str = Integer.toHexString(mac[i] & 0xFF);
                            sb.append(str.length() == 1 ? 0 + str : str);
                        }
                        macs = sb.toString().toUpperCase();
                        // System.out.println("外网 - localip: " + localip);
                        // System.out.println("外网 - macs: " + macs);
                        finded = true;
                        break;
                    } else if (inetAddress.isSiteLocalAddress() && !inetAddress.isLoopbackAddress()) { //内网
                        localip = inetAddress.getHostAddress();
                        byte[] mac = ni.getHardwareAddress();
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < mac.length; i++) {
                            if (i != 0) {
                                sb.append("-");
                            }
                            String str = Integer.toHexString(mac[i] & 0xFF);
                            sb.append(str.length() == 1 ? 0 + str : str);
                        }
                        macs = sb.toString().toUpperCase();
                        // System.out.println("内网 - localip: " + localip);
                        // System.out.println("内网 - macs: " + macs);
                        finded = true;
                        break;
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return ipAddress ? localip : macs;
    }
    
    /*
     * 获取Linux系统的MAC地址和IP地址（针对Linux系统优化）
     * ipAddress = true 取IP地址，false 取MAC地址
     */
    private static String getLinuxMacAddress(boolean ipAddress) {
        String macAddress = null;
        String ipAddr = null;
        
        try {
            // 第一优先级：获取物理接口信息（按优先级排序）
            List<String> physicalInterfaces = getLinuxPhysicalInterfaces();
            
            for (String interfaceName : physicalInterfaces) {
                try {
                    NetworkInterface networkInterface = NetworkInterface.getByName(interfaceName);
                    if (networkInterface == null || networkInterface.isLoopback() || !networkInterface.isUp()) {
                        continue;
                    }
                    
                    // 获取MAC地址
                    byte[] mac = networkInterface.getHardwareAddress();
                    if (mac != null && mac.length > 0) {
                        StringBuilder sb = new StringBuilder();
                        for (int i = 0; i < mac.length; i++) {
                            if (i != 0) {
                                sb.append("-");
                            }
                            String str = Integer.toHexString(mac[i] & 0xFF);
                            sb.append(str.length() == 1 ? 0 + str : str);
                        }
                        macAddress = sb.toString().toUpperCase();
                        
                        // 获取IP地址（优先获取IPv4）
                        Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                        while (addresses.hasMoreElements()) {
                            InetAddress addr = addresses.nextElement();
                            if (!addr.isLoopbackAddress() && addr instanceof Inet4Address) {
                                ipAddr = addr.getHostAddress();
                                // 如果已经找到了MAC和IP，立即返回
                                return ipAddress ? ipAddr : macAddress;
                            }
                        }
                    }
                } catch (Exception e) {
                    // 忽略单个接口的异常，继续尝试下一个接口
                }
            }
            
            // 如果通过优先级接口未找到，尝试遍历所有接口
            if (macAddress == null || ipAddr == null) {
                Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
                while (interfaces.hasMoreElements()) {
                    NetworkInterface networkInterface = interfaces.nextElement();
                    String interfaceName = networkInterface.getName();
                    
                    // 跳过回环接口、虚拟接口和已关闭接口
                    if (networkInterface.isLoopback() || !networkInterface.isUp() || 
                        interfaceName.contains("docker") || interfaceName.contains("veth") || 
                        interfaceName.equals("lo")) {
                        continue;
                    }
                    
                    // 获取MAC地址
                    byte[] mac = networkInterface.getHardwareAddress();
                    if (mac == null || mac.length == 0) {
                        continue;
                    }
                    
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < mac.length; i++) {
                        if (i != 0) {
                            sb.append("-");
                        }
                        String str = Integer.toHexString(mac[i] & 0xFF);
                        sb.append(str.length() == 1 ? 0 + str : str);
                    }
                    macAddress = sb.toString().toUpperCase();
                    
                    // 获取IP地址（优先IPv4）
                    Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        InetAddress addr = addresses.nextElement();
                        if (!addr.isLoopbackAddress() && addr instanceof Inet4Address) {
                            ipAddr = addr.getHostAddress();
                            break;
                        }
                    }
                    
                    // 如果找到了MAC和IP，退出循环
                    if (macAddress != null && ipAddr != null) {
                        break;
                    }
                }
            }
            
            // 如果通过Java API无法获取，尝试通过命令行获取（需要root权限）
            if (macAddress == null) {
                macAddress = getLinuxMacAddressFromCommand();
            }
            
            // 如果通过Java API无法获取IP，尝试通过命令行获取
            if (ipAddr == null) {
                ipAddr = getLinuxIPAddressFromCommand();
            }
            
        } catch (Exception e) {
            // 如果出现异常，回退到通用方法
            return getGenericMacAddress(ipAddress);
        }
        
        return ipAddress ? ipAddr : macAddress;
    }
    
    /*
     * 获取Linux物理网络接口列表，按优先级排序
     */
    private static List<String> getLinuxPhysicalInterfaces() {
        List<String> interfaces = new ArrayList<>();
        
        try {
            // 获取所有网络接口名称
            Set<String> allInterfaces = new HashSet<>();
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface networkInterface = netInterfaces.nextElement();
                allInterfaces.add(networkInterface.getName());
            }
            
            // 按常见物理接口名称的优先级添加
            // 优先使用常见的以太网接口
            for (String prefix : Arrays.asList("eth", "en", "em", "eno", "enp", "ens")) {
                for (String iface : allInterfaces) {
                    if (iface.startsWith(prefix)) {
                        interfaces.add(iface);
                    }
                }
            }
            
            // 添加无线网络接口
            for (String prefix : Arrays.asList("wlan", "wl", "wifi", "wlp")) {
                for (String iface : allInterfaces) {
                    if (iface.startsWith(prefix)) {
                        interfaces.add(iface);
                    }
                }
            }
            
            // 添加其他可能的物理接口
            for (String iface : allInterfaces) {
                if (!interfaces.contains(iface) && 
                    !iface.contains("docker") && 
                    !iface.contains("veth") && 
                    !iface.equals("lo") &&
                    !iface.startsWith("br") &&
                    !iface.startsWith("virbr")) {
                    interfaces.add(iface);
                }
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return interfaces;
    }
    
    /*
     * 通过命令行获取Linux的MAC地址
     */
    private static String getLinuxMacAddressFromCommand() {
        try {
            // 尝试使用ip命令（大多数现代Linux发行版都有）
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "ip link | grep -E 'state UP' -A1 | grep -E 'link/ether' | head -n1 | awk '{print $2}' | tr ':' '-'"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                return line.trim().toUpperCase();
            }
            
            // 如果ip命令失败，尝试使用ifconfig命令
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "ifconfig | grep -E 'eth|en|em|eno|enp|ens' -A1 | grep -E 'ether|HWaddr' | head -n1 | awk '{print $2}' | tr ':' '-'"});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                return line.trim().toUpperCase();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return null;
    }
    
    /*
     * 通过命令行获取Linux的IP地址
     */
    private static String getLinuxIPAddressFromCommand() {
        try {
            // 尝试使用ip命令
            Process p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "ip -4 addr show scope global | grep -oP '(?<=inet\\s)\\d+(\\.\\d+){3}' | head -n1"});
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                return line.trim();
            }
            
            // 如果ip命令失败，尝试使用ifconfig命令
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", 
                    "ifconfig | grep -E 'eth|en|em|eno|enp|ens' -A2 | grep -oP '(?<=inet\\s)\\d+(\\.\\d+){3}' | head -n1"});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                return line.trim();
            }
            
            // 最后尝试使用hostname命令
            p = Runtime.getRuntime().exec(new String[]{"bash", "-c", "hostname -I | awk '{print $1}'"});
            input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            line = input.readLine();
            input.close();
            
            if (line != null && !line.trim().isEmpty()) {
                return line.trim();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return null;
    }
}