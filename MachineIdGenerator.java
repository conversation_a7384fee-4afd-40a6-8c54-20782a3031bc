package tools;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;


public class MachineIdGenerator {
    /**
     * String[7Td^(w_UnU.C<2O1]
     * 解密常量[STABLE_SALT]的密钥
     */
    public static final byte[] STABLE_SALT_KEY = new byte[]{
            (byte) 0x37, (byte) 0x54, (byte) 0x64, (byte) 0x5E, (byte) 0x28, (byte) 0x77, (byte) 0x5F, (byte) 0x55, (byte) 0x6E, (byte) 0x55, (byte) 0x2E, (byte) 0x43, (byte) 0x3C, (byte) 0x32, (byte) 0x4F, (byte) 0x31,
    };

    /**
     * 原始字符串[sdkjfhdjkshKJHBFDSKJF8S7CJIABskfdhkjasdh9SBHD]
     * 字符串内容已混淆
     * 用途:
     */
    public static final byte[] STABLE_SALT = {
            (byte) 0x73, (byte) 0x1C, (byte) 0x26, (byte) 0x0D, (byte) 0x11, (byte) 0x1F, (byte) 0x3B, (byte) 0x26, (byte) 0x0F, (byte) 0x3F, (byte) 0x45, (byte) 0x2B, (byte) 0x58, (byte) 0x54, (byte) 0x24, (byte) 0x42,
            (byte) 0x75, (byte) 0x15, (byte) 0x2D, (byte) 0x14, (byte) 0x6B, (byte) 0x40, (byte) 0x0C, (byte) 0x6D, (byte) 0x28, (byte) 0x1F, (byte) 0x65, (byte) 0x10, (byte) 0x78, (byte) 0x74, (byte) 0x0D, (byte) 0x79,
            (byte) 0x7D, (byte) 0x1F, (byte) 0x0C, (byte) 0x2D, (byte) 0x43, (byte) 0x1D, (byte) 0x3B, (byte) 0x3D, (byte) 0x08, (byte) 0x3F, (byte) 0x45, (byte) 0x27, (byte) 0x4F,
    };


    /**
     * String[Ebagi-mDtJ,{BJBQ]
     * 解密常量[ANTIFORGE_SALT_1]的密钥
     */
    public static final byte[] ANTIFORGE_SALT_1_KEY = new byte[]{
            (byte) 0x45, (byte) 0x62, (byte) 0x61, (byte) 0x67, (byte) 0x69, (byte) 0x2D, (byte) 0x6D, (byte) 0x44, (byte) 0x74, (byte) 0x4A, (byte) 0x2C, (byte) 0x7B, (byte) 0x42, (byte) 0x4A, (byte) 0x42, (byte) 0x51,
    };

    /**
     * 原始字符串[fkdjashfkdjhKHJASDKH78623KNsjdbgsbb_nh197h]
     * 字符串内容已混淆
     * 用途:
     */
    public static final byte[] ANTIFORGE_SALT_1 = {
            (byte) 0x2D, (byte) 0x55, (byte) 0x58, (byte) 0x56, (byte) 0x01, (byte) 0x43, (byte) 0x32, (byte) 0x26, (byte) 0x16, (byte) 0x39, (byte) 0x4B, (byte) 0x19, (byte) 0x26, (byte) 0x20, (byte) 0x31, (byte) 0x1F,
            (byte) 0x0E, (byte) 0x51, (byte) 0x53, (byte) 0x51, (byte) 0x51, (byte) 0x1A, (byte) 0x25, (byte) 0x0F, (byte) 0x30, (byte) 0x19, (byte) 0x6D, (byte) 0x31, (byte) 0x0A, (byte) 0x01, (byte) 0x2A, (byte) 0x3B,
            (byte) 0x21, (byte) 0x09, (byte) 0x07, (byte) 0x0F, (byte) 0x1A, (byte) 0x4C, (byte) 0x07, (byte) 0x20, (byte) 0x1F, (byte) 0x2C,
    };


    /**
     * String[(fib>&f5V'|}!Hh2]
     * 解密常量[ANTIFORGE_SALT_2]的密钥
     */
    public static final byte[] ANTIFORGE_SALT_2_KEY = new byte[]{
            (byte) 0x28, (byte) 0x66, (byte) 0x69, (byte) 0x62, (byte) 0x3E, (byte) 0x26, (byte) 0x66, (byte) 0x35, (byte) 0x56, (byte) 0x27, (byte) 0x7C, (byte) 0x7D, (byte) 0x21, (byte) 0x48, (byte) 0x68, (byte) 0x32,
    };

    /**
     * 原始字符串[SJHDkjnsdhasSUHDBNWNnjknqdol_12nhbnh]
     * 字符串内容已混淆
     * 用途:
     */
    public static final byte[] ANTIFORGE_SALT_2 = {
            (byte) 0x40, (byte) 0x08, (byte) 0x0B, (byte) 0x0A, (byte) 0x50, (byte) 0x14, (byte) 0x57, (byte) 0x6A, (byte) 0x3A, (byte) 0x48, (byte) 0x18, (byte) 0x0C, (byte) 0x4F, (byte) 0x23, (byte) 0x02, (byte) 0x5C,
            (byte) 0x66, (byte) 0x31, (byte) 0x27, (byte) 0x20, (byte) 0x7A, (byte) 0x6E, (byte) 0x33, (byte) 0x66, (byte) 0x25, (byte) 0x46, (byte) 0x14, (byte) 0x19, (byte) 0x52, (byte) 0x26, (byte) 0x02, (byte) 0x59,
            (byte) 0x6C, (byte) 0x2E, (byte) 0x23, (byte) 0x31,
    };


    public static String getMachineId() {
//        if(isVirtualEnvironment()) {//暂时不检查
//            System.err.println(" 服务器存在虚拟化信息。");
//            return null;
//        }
        StringBuilder result = new StringBuilder();
        Map<String, String> antiForging = new HashMap<>();
        // 1. CPU序列号
        String cpu = getCPUSerial();
        if(cpu == null || cpu.trim().isEmpty()) {
            logGetMachine(3, " 1-1");
            return null;
        }
        result.append(cpu);
        antiForging.put("cpu_info", cpu);

        // 2. 硬盘序列号
        String diskSN = getHardDiskSN("c");
        if(diskSN == null || diskSN.trim().isEmpty()) {
            logGetMachine(3, " 2-2");
            return null;
        }
        result.append(diskSN);
        antiForging.put("diskSN_info", diskSN);

//        // 3. 获取主板序列号
//        String motherboard = getMotherboardSerial();
//        if(motherboard == null || motherboard.trim().isEmpty()) {
//            logGetMachine(3, " 3-3");
//            return null;
//        }
//        result.append(motherboard);
//        antiForging.put("motherboard_info", motherboard);
//
//        // 4. 获取BISO信息
//        String biosInfo = getBIOSInfo();
//        if(biosInfo == null || biosInfo.trim().isEmpty()) {
//            logGetMachine(3, " 4-4");
//            return null;
//        }
//        result.append(biosInfo);
//        antiForging.put("biosInfo_info", biosInfo);
//
//        // 5. 获取系统安装时间和系统ID
//        String systemInstallInfo = getSystemInstallInfo();
//        if(systemInstallInfo == null || systemInstallInfo.trim().isEmpty()) {
//            logGetMachine(3, " 5-5");
//            return null;
//        }
//        result.append(systemInstallInfo);
//        antiForging.put("systemInstallInfo_info", systemInstallInfo);

        // 4. MAC地址
        String mac = MacAddressTool.getMacAddress(false);
        if(mac != null) {
            result.append(mac);
            antiForging.put("mac_info", mac);
        }
        // 生成稳定指纹字符串
        String stableFingerprint = generateStableFingerprint(antiForging);
        // 添加防伪造层
        return addAntiForgingLayer(stableFingerprint, antiForging);
    }

    /**
     * 检测是否为虚拟环境
     */
    public static boolean isVirtualEnvironment() {
        // 检测多个虚拟化特征
        return checkVMwareSignatures() || checkVirtualBoxSignatures() || checkHyperVSignatures() || checkQEMUSignatures() || checkVMProcesses() || checkVMRegistry() || checkVMHardware();
    }

    /**
     * 获取CPU序列号
     */
    public static String getCPUSerial() {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("CPUSerial", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery _ \n"
                    + "   (\"Select * from Win32_Processor\") \n"
                    + "For Each objItem in colItems \n"
                    + "    Wscript.Echo objItem.ProcessorId \n"
                    + "    exit for  ' do the first cpu only! \n" + "Next \n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line);
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 1");
            result = null;
        }
        if(result == null || result.toString().trim().isEmpty()) {
            result = null;
        }
        if(result != null) {
            return result.toString().trim();
        }
        return null;
    }

    /**
     * 获取硬盘序列号
     *
     * @param drive 盘符
     */
    public static String getHardDiskSN(String drive) {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("disksn", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new java.io.FileWriter(file);
            String vbs = "Set objFSO = CreateObject(\"Scripting.FileSystemObject\")\n"
                    + "Set colDrives = objFSO.Drives\n"
                    + "Set objDrive = colDrives.item(\""
                    + drive
                    + "\")\n"
                    + "Wscript.Echo objDrive.SerialNumber"; // see note
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line);
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 2");
            result = null;
        }
        if(result != null) {
            return result.toString().trim();
        }
        return null;
    }

    /**
     * 获取多硬盘序列号
     */
    public static String getAllDiskSerials() {
        StringBuilder result = new StringBuilder();
        String[] drives = {"c", "d", "e", "f"};
        for(String drive : drives) {
            String serial = getHardDiskSN(drive);
            if(serial != null && !serial.trim().isEmpty()) {
                result.append(serial);
            }
        }
        return result.toString();
    }

    /**
     * 获取主板序列号
     */
    public static String getMotherboardSerial() {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("motherboard", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BaseBoard\")\n"
                    + "For Each objItem in colItems\n"
                    + "    If objItem.SerialNumber <> \"\" Then\n"
                    + "        Wscript.Echo objItem.SerialNumber\n"
                    + "        Exit For\n"
                    + "    End If\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                if(!line.trim().isEmpty()) {
                    result.append(line.trim());
                }
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            // 备用方案：获取主板型号
            try {
                File file = File.createTempFile("motherboard_model", ".vbs");
                file.deleteOnExit();
                FileWriter fw = new FileWriter(file);
                String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                        + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BaseBoard\")\n"
                        + "For Each objItem in colItems\n"
                        + "    Wscript.Echo objItem.Product & \"|\" & objItem.Manufacturer\n"
                        + "    Exit For\n"
                        + "Next\n";
                fw.write(vbs);
                fw.close();
                Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
                BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
                String line;
                while ((line = input.readLine()) != null) {
                    result.append(line.trim());
                }
                input.close();
                file.delete();
            } catch (Exception ex) {
                logGetMachine(3, " 3");
                return null;
            }
        }
        return !result.isEmpty() ? result.toString() : null;
    }

    /**
     * 获取BIOS信息
     */
    public static String getBIOSInfo() {
        StringBuilder result = new StringBuilder();
        try {
            File file = File.createTempFile("bios", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_BIOS\")\n"
                    + "For Each objItem in colItems\n"
                    + "    Wscript.Echo objItem.SerialNumber & \"|\" & objItem.ReleaseDate\n"
                    + "    Exit For\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line.trim());
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 4");
            return null;
        }
        return !result.isEmpty() ? result.toString() : null;
    }

    /**
     * 获取系统安装时间和系统ID
     */
    public static String getSystemInstallInfo() {
        StringBuilder result = new StringBuilder();
        try {
            // 获取系统安装日期
            File file = File.createTempFile("sysinstall", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_OperatingSystem\")\n"
                    + "For Each objItem in colItems\n"
                    + "    Wscript.Echo objItem.InstallDate & \"|\" & objItem.SerialNumber\n"
                    + "    Exit For\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                result.append(line.trim());
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 5");
            return null;
        }
        return !result.isEmpty() ? result.toString() : null;
    }

    /**
     * 检测VMware特征
     */
    private static boolean checkVMwareSignatures() {
        try {
            // 检测VMware BIOS
            String biosInfo = getBIOSInfo();
            if(biosInfo != null && (biosInfo.toLowerCase().contains("vmware") || biosInfo.toLowerCase().contains("phoenix technologies"))) {
                return true;
            }

            // 检测VMware主板
            String motherboard = getMotherboardSerial();
            if(motherboard != null && motherboard.toLowerCase().contains("vmware")) {
                return true;
            }

            // 检测VMware MAC地址前缀
            String mac = MacAddressTool.getMacAddress(false);
            if(mac != null) {
                String[] vmwareMacPrefixes = {"00-0C-29", "00-1C-14", "00-50-56"};
                for(String prefix : vmwareMacPrefixes) {
                    if(mac.startsWith(prefix)) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            logGetMachine(3, " 6");
        }
        return false;
    }

    /**
     * 检测VirtualBox特征
     */
    private static boolean checkVirtualBoxSignatures() {
        try {
            String biosInfo = getBIOSInfo();
            if(biosInfo != null && biosInfo.toLowerCase().contains("virtualbox")) {
                return true;
            }

            String motherboard = getMotherboardSerial();
            if(motherboard != null && motherboard.toLowerCase().contains("oracle")) {
                return true;
            }

            // VirtualBox MAC地址前缀
            String mac = MacAddressTool.getMacAddress(false);
            if(mac != null && mac.startsWith("08-00-27")) {
                return true;
            }
        } catch (Exception e) {
            logGetMachine(3, " 7");
        }
        return false;
    }

    /**
     * 检测Hyper-V特征
     */
    private static boolean checkHyperVSignatures() {
        try {
            String biosInfo = getBIOSInfo();
            if(biosInfo != null && biosInfo.toLowerCase().contains("microsoft")) {
                return true;
            }

            // Hyper-V MAC地址前缀
            String mac = MacAddressTool.getMacAddress(false);
            if(mac != null && mac.startsWith("00-15-5D")) {
                return true;
            }
        } catch (Exception e) {
            logGetMachine(3, " 8");
        }
        return false;
    }

    /**
     * 检测QEMU特征
     */
    private static boolean checkQEMUSignatures() {
        try {
            String biosInfo = getBIOSInfo();
            if(biosInfo != null && (biosInfo.toLowerCase().contains("qemu") ||
                    biosInfo.toLowerCase().contains("bochs"))) {
                return true;
            }

            // QEMU MAC地址前缀
            String mac = MacAddressTool.getMacAddress(false);
            if(mac != null && (mac.startsWith("52-54-00") || mac.startsWith("00-16-3E"))) {
                return true;
            }
        } catch (Exception e) {
            logGetMachine(3, " 9");
        }
        return false;
    }

    /**
     * 检测虚拟机进程
     */
    private static boolean checkVMProcesses() {
        try {
            File file = File.createTempFile("vmprocess", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_Process\")\n"
                    + "For Each objItem in colItems\n"
                    + "    processName = LCase(objItem.Name)\n"
                    + "    If InStr(processName, \"vmware\") > 0 Or InStr(processName, \"virtualbox\") > 0 Or InStr(processName, \"vbox\") > 0 Or InStr(processName, \"qemu\") > 0 Then\n"
                    + "        Wscript.Echo \"VM_DETECTED\"\n"
                    + "        Exit For\n"
                    + "    End If\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                if(line.contains("VM_DETECTED")) {
                    input.close();
                    file.delete();
                    return true;
                }
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 10");
        }
        return false;
    }

    /**
     * 检测虚拟机注册表特征
     */
    private static boolean checkVMRegistry() {
        try {
            File file = File.createTempFile("vmregistry", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "On Error Resume Next\n"
                    + "Set objShell = CreateObject(\"WScript.Shell\")\n"
                    + "vmwareKey = objShell.RegRead(\"HKLM\\SOFTWARE\\VMware, Inc.\\VMware Tools\\\")\n"
                    + "If Err.Number = 0 Then\n"
                    + "    Wscript.Echo \"VMWARE_DETECTED\"\n"
                    + "End If\n"
                    + "Err.Clear\n"
                    + "vboxKey = objShell.RegRead(\"HKLM\\SOFTWARE\\Oracle\\VirtualBox Guest Additions\\\")\n"
                    + "If Err.Number = 0 Then\n"
                    + "    Wscript.Echo \"VBOX_DETECTED\"\n"
                    + "End If\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                if(line.contains("_DETECTED")) {
                    input.close();
                    file.delete();
                    return true;
                }
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 11");
        }
        return false;
    }

    /**
     * 检测虚拟机硬件特征
     */
    private static boolean checkVMHardware() {
        try {
            File file = File.createTempFile("vmhardware", ".vbs");
            file.deleteOnExit();
            FileWriter fw = new FileWriter(file);
            String vbs = "Set objWMIService = GetObject(\"winmgmts:\\\\.\\root\\cimv2\")\n"
                    + "Set colItems = objWMIService.ExecQuery(\"Select * from Win32_ComputerSystem\")\n"
                    + "For Each objItem in colItems\n"
                    + "    model = LCase(objItem.Model)\n"
                    + "    manufacturer = LCase(objItem.Manufacturer)\n"
                    + "    If InStr(model, \"virtual\") > 0 Or InStr(model, \"vmware\") > 0 Or InStr(manufacturer, \"vmware\") > 0 Or InStr(manufacturer, \"microsoft corporation\") > 0 Then\n"
                    + "        Wscript.Echo \"VM_HARDWARE_DETECTED\"\n"
                    + "        Exit For\n"
                    + "    End If\n"
                    + "Next\n";
            fw.write(vbs);
            fw.close();
            Process p = Runtime.getRuntime().exec("cscript //NoLogo " + file.getPath());
            BufferedReader input = new BufferedReader(new InputStreamReader(p.getInputStream()));
            String line;
            while ((line = input.readLine()) != null) {
                if(line.contains("VM_HARDWARE_DETECTED")) {
                    input.close();
                    file.delete();
                    return true;
                }
            }
            input.close();
            file.delete();
        } catch (Exception e) {
            logGetMachine(3, " 12");
        }
        return false;
    }


    /**
     * 生成稳定的主指纹
     */
    private static String generateStableFingerprint(Map<String, String> stableHardware) {
        StringBuilder fingerprint = new StringBuilder();
        String[] priorityFields = {
                "cpu_info",
                "diskSN_info",
//                "motherboard_info",
//                "biosInfo_info",
//                "systemInstallInfo_info",
                "mac_info",
        };
        // 构建分层指纹
        int validFieldCount = 0;
        // System.out.println("🔨 构建稳定指纹:");
        for(String field : priorityFields) {
            String value = stableHardware.get(field);
            if(value != null && !value.trim().isEmpty()) {
                fingerprint.append(field).append("=").append(value).append("||");
                validFieldCount++;
                //  System.out.println("  ✓ " + field + " = " + maskSensitiveInfo(value));
            }
        }
        // 添加字段统计信息
        fingerprint.append("FIELD_COUNT=").append(validFieldCount);
//        // 确保至少有3个核心字段
//        if(validFieldCount < 3) {
//            logAnti(2," d - " + validFieldCount);
//        }
        return fingerprint.toString();
    }

    /**
     * 添加防伪造层
     */
    private static String addAntiForgingLayer(String stableFingerprint, Map<String, String> antiForgingInfo) {
        try {
            String baseHash = stableHash(stableFingerprint);
            //logAnti(1,"🔐 基础哈希: \" + baseHash.substring(0, 16) + \"...");
            // 创建防伪造验证码
            StringBuilder antiForgingLayer = new StringBuilder();
            String cpu = antiForgingInfo.get("cpu_info");
            String diskSN = antiForgingInfo.get("diskSN_info");
//            String motherboard = antiForgingInfo.get("motherboard_info");
//            String biosInfo = antiForgingInfo.get("biosInfo_info");
//            String systemInstallInfo = antiForgingInfo.get("systemInstallInfo_info");
            String mac = antiForgingInfo.get("mac_info");
            if(cpu != null) {
                antiForgingLayer.append("CSD:").append(cpu).append(";");
            }
            if(diskSN != null) {
                antiForgingLayer.append("OIU:").append(diskSN).append(";");
            }
//            if(motherboard != null) {
//                antiForgingLayer.append("OJH:").append(motherboard).append(";");
//            }
//            if(biosInfo != null) {
//                antiForgingLayer.append("KUI:").append(biosInfo).append(";");
//            }
//            if(systemInstallInfo != null) {
//                antiForgingLayer.append("KIS:").append(systemInstallInfo).append(";");
//            }
            if(mac != null) {
                antiForgingLayer.append("NMW:").append(mac).append(";");
            }
            // 组合稳定指纹和防伪造层
            String combinedFingerprint = baseHash + "::ISOKDIUNW::" + antiForgingLayer;
            // 最终哈希
            String finalHash = enhancedStableHash(combinedFingerprint);
//            logAnti(1," 最终指纹: " + finalHash.substring(0, 16) + "...");
            return finalHash;
        } catch (Exception e) {
            //防伪造层处理失败，使用基础指纹
            logAnti(3," 1");
            return stableHash(stableFingerprint);
        }
    }


    /**
     * 稳定的哈希算法
     */
    private static String stableHash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String saltedInput = input + "::" + new String(StringUtil.decryXOR(STABLE_SALT, STABLE_SALT_KEY), StandardCharsets.UTF_8) + "::" + input.length();
            byte[] hash = digest.digest(saltedInput.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (Exception e) {
            //稳定哈希生成失败
            logAnti(3," 2");
            throw new RuntimeException();
        }
    }

    /**
     * 增强的稳定哈希（包含防伪造层）
     */
    private static String enhancedStableHash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            String[] salts = {new String(StringUtil.decryXOR(ANTIFORGE_SALT_1, ANTIFORGE_SALT_1_KEY), StandardCharsets.UTF_8), new String(StringUtil.decryXOR(ANTIFORGE_SALT_2, ANTIFORGE_SALT_2_KEY), StandardCharsets.UTF_8)};
            String currentHash = input;
            // 两轮哈希：第一轮保证稳定性，第二轮增加防伪造
            for(int i = 0; i < 2; i++) {
                String saltedInput = currentHash + "::" + salts[i] + "::" + (i + 1);
                byte[] hash = digest.digest(saltedInput.getBytes(StandardCharsets.UTF_8));
                currentHash = bytesToHex(hash);
                digest.reset();
            }
            return currentHash;
        } catch (Exception e) {
            return stableHash(input); // 降级到稳定哈希
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for(byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 发送防伪层调试信息
     *
     * @param mode    1=正常    2=警告     3=错误
     * @param message 信息
     */
    public static void logAnti(int mode, String message) {
        switch (mode) {
            case 2:
                System.err.println("Internal warn: " + message);
                break;
            case 3:
                System.err.println("Internal errors " + message);
                break;
            default:
                System.err.println("Internal info: " + message);
                break;
        }
    }

    /**
     * 发送获取机器信息调试信息
     *
     * @param mode    1=正常    2=警告     3=错误
     * @param message 信息
     */
    public static void logGetMachine(int mode, String message) {
        switch (mode) {
            case 2:
                System.err.println("Calculate warn: " + message);
                break;
            case 3:
                System.err.println("Calculate errors " + message);
                break;
            default:
                System.err.println("Calculate info: " + message);
                break;
        }
    }
}