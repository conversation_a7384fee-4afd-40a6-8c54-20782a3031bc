<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
<modelVersion>4.0.0</modelVersion>

<!-- 目的组织标识符 -->
<groupId>RyanFan</groupId>
<!-- 项目的唯一标识符 -->
    <artifactId>authorization</artifactId>
<!-- 项目的版本号 -->
<version>1.0.0</version>
<!-- 项目的打包类型，这里设置为 jar -->
<packaging>jar</packaging>

<properties>
    <!-- 报告输出的编码格式 -->
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <!-- 源代码文件的编码格式 -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- 项目使用的 Java 版本 -->
    <java.version>21</java.version>
    <!-- 编译器源代码版本 -->
    <maven.compiler.source>21</maven.compiler.source>
    <!-- 编译器目标代码版本 -->
    <maven.compiler.target>21</maven.compiler.target>
    <!-- 构建输出的最终 JAR 文件名称 -->
    <build.name>authorization</build.name>
    <!-- 配置库路径 -->
    <compiler.dir>${project.build.directory}/lib</compiler.dir>
    <!-- 主类路径 -->
    <main.class>org.example.Main</main.class>
    <!-- 设定的字符 -->
    <jvm.encoding.argLine>-Dfile.encoding=UTF-8</jvm.encoding.argLine>
    <!--
         BRIEF: 输出简要信息，只显示最重要的警告和错误。 适合快速查看关键问题。
         DEFAULT: 输出默认详细程度的信息。 适合大多数情况，提供了合理的详细程度。
         VERBOSE: 输出详细信息，包括所有警告和错误。 适合需要详细了解所有潜在问题的情况。
    -->
    <maven.plugin.validation>VERBOSE</maven.plugin.validation>
</properties>

<build>
    <!-- 设置构建输出的最终 JAR/WAR 文件名称 -->
    <finalName>${build.name}</finalName>
    <!-- 指定源代码目录 -->
    <sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
    <resources>
        <resource>
            <!-- 配置资源文件的目录为 -->
            <directory>src/main/resources</directory>
            <!-- 启用过滤功能 -->
            <filtering>true</filtering>
        </resource>
    </resources>

    <plugins>
        <plugin>
            <!--
                https://maven.apache.org/surefire/maven-surefire-plugin/
                主要功能：
                1: 执行项目中的单元测试（JUnit/TestNG）
                2: 生成测试报告（target/surefire-reports）
                3: 支持并行测试执行（提高测试效率）
                4: 提供测试过滤机制（按类名/方法名/分组运行）
                5: 支持多种测试框架（通过 Provider 机制）
                配置说明：
                1: <skip>true</skip> - 完全跳过测试执行（编译阶段仍会编译测试代码）
                2: <argLine>${jvm.encoding.argLine}</argLine> - 设置测试 JVM 的默认编码为 UTF-8
                3: 其他重要参数：
                   - <forkCount> - 控制测试进程数（默认 1）
                   - <includes> - 指定要运行的测试（如 **/*Test.java）
                   - <excludes> - 排除特定测试
                注意事项：
                1: 跳过测试会同时跳过单元测试和集成测试
                2: 建议在 CI/CD 中保持 skip=false，仅在本地开发时临时跳过
                3: UTF-8 编码可避免中文字符乱码导致的测试失败
            -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <version>3.5.2</version>
            <configuration>
                <!-- 跳过测试 -->
                <skip>true</skip>
                <!-- 传递给测试 JVM 的参数 -->
                <argLine>${jvm.encoding.argLine}</argLine>
            </configuration>
        </plugin>

        <plugin>
            <!--
                https://maven.apache.org/plugins/maven-jar-plugin/
                主要功能：
                1: 将项目编译后的类文件和资源打包为 JAR 文件
                2: 生成并配置 MANIFEST.MF 文件（包含元数据信息）
                3: 支持自定义类路径（Class-Path）配置
                4: 提供 JAR 文件的签名功能（需配合 jarsigner）
                5: 用于打包 JAR 文件，配置了 MANIFEST 文件，添加类路径前缀 lib/ 并指定主类
                配置说明：
                1: <addMavenDescriptor>false</addMavenDescriptor> - 不在 JAR 中包含 Maven 描述文件（pom.xml/pom.properties）
                2: <addClasspath>true</addClasspath> - 在 MANIFEST 中添加 Class-Path 条目
                3: <classpathPrefix>lib/</classpathPrefix> - 为依赖 JAR 添加路径前缀（表示依赖位于 lib/ 目录）
                4: 默认输出位置：target/${project.build.finalName}.jar
                注意事项：
                1: 此插件仅打包项目自身代码，不包含依赖（需配合 maven-dependency-plugin 复制依赖）
                2: Class-Path 条目需与依赖文件实际存放位置匹配
                3: 主类配置（如需要）需额外添加 <mainClass>com.example.Main</mainClass>
            -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <version>3.3.0</version>
            <configuration>
                <archive>
                    <!-- 不包含 Maven 描述符（减少 JAR 体积约 1KB） -->
                    <addMavenDescriptor>false</addMavenDescriptor>
                    <manifest>
                        <!-- 启用类路径自动生成（必需功能） -->
                        <addClasspath>true</addClasspath>
                        <!--
                             类路径前缀：所有依赖 JAR 将位于 lib/ 目录
                             最终 MANIFEST 示例：Class-Path: lib/dependency1.jar lib/dependency2.jar
                        -->
                        <classpathPrefix>lib/</classpathPrefix>
                        <!-- 指定主类 -->
                        <mainClass>${main.class}</mainClass>
                    </manifest>
                </archive>
            </configuration>
        </plugin>


        <!-- 在 package 阶段将依赖项复制到 ${project.build.directory}/lib 目录 -->
        <plugin>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-dependency-plugin -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <version>3.6.1</version>
            <executions>
                <execution>
                    <id>copy-dependencies</id>
                    <!-- 在 package 生命周期阶段执行 -->
                    <phase>package</phase>
                    <goals>
                        <!-- 执行 copy-dependencies -->
                        <goal>copy-dependencies</goal>
                    </goals>
                    <configuration>
                        <!-- 指定输出目录，表示项目的构建输出目录，默认通常是 target 目录，表示编译输出目录） -->
                        <outputDirectory>${compiler.dir}</outputDirectory>
                        <!-- 覆盖已发布的依赖项 -->
                        <overWriteReleases>true</overWriteReleases>
                        <!-- 覆盖快照依赖项 -->
                        <overWriteSnapshots>true</overWriteSnapshots>
                        <!--- 如果目标文件较新，则覆盖 -->
                        <overWriteIfNewer>true</overWriteIfNewer>
                    </configuration>
                </execution>
            </executions>
        </plugin>

        <!-- 配置资源文件的 -->
        <plugin>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-resources-plugin -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>3.3.1</version>
            <configuration>
                <!-- 全局编码设置，影响所有资源文件 -->
                <encoding>UTF-8</encoding>
                <!-- .properties 文件的编码设置 -->
                <propertiesEncoding>UTF-8</propertiesEncoding>
            </configuration>
        </plugin>

        <!-- 用于编译 Java 源代码，指定了源代码和目标代码的版本为 ${java.version}-->
        <plugin>
            <!-- https://mvnrepository.com/artifact/org.apache.maven.plugins/maven-compiler-plugin -->
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <version>3.13.0</version>
            <configuration>
                <encoding>UTF-8</encoding>
                <source>${java.version}</source>
                <target>${java.version}</target>
            </configuration>
        </plugin>
    </plugins>
</build>

<!-- 插件 -->
<profiles>
    <profile>
        <id>native</id>
        <build>
            <plugins>
                <plugin>
                    <!-- https://mvnrepository.com/artifact/org.graalvm.buildtools/native-maven-plugin -->
                    <groupId>org.graalvm.buildtools</groupId>
                    <artifactId>native-maven-plugin</artifactId>
                    <version>0.10.2</version>
                    <!-- 启用插件扩展，允许插件在 Maven 生命周期中更早地参与构建过程 -->
                    <extensions>true</extensions>
                    <executions>
                        <execution>
                            <id>build-native</id>
                            <goals>
                                <!-- 在Linux环境中使用compile代替compile-no-fork -->
                                <goal>compile</goal>
                            </goals>
                            <phase>package</phase>
                        </execution>
                    </executions>
                    <configuration>
                        <!-- 恢复原有配置并添加G1垃圾回收器 -->
                        <imageName>${build.name}</imageName>
                        <mainClass>${main.class}</mainClass>
                        <outputDirectory>/home/<USER>/GraalVmOut/${project.artifactId}</outputDirectory>
                        <buildArgs>
                            <!-- 架构优化参数 - 通用于Windows/Linux -->
                            <buildArg>-march=x86-64-v3</buildArg>                                           <!-- 指定目标架构为 x86-64-v3 -->
                            <!-- 内存和GC配置 -->
                            <buildArg>--strict-image-heap</buildArg>                                        <!-- 启用严格的镜像堆模式 -->
                            <buildArg>--gc=G1</buildArg>                                                    <!-- 使用G1垃圾回收器 -->
                            <buildArg>-H:+UnlockExperimentalVMOptions</buildArg>                            <!-- 解锁实验性的VM选项 -->
                            <!-- 确保完全原生编译，无JVM依赖 -->
                            <buildArg>--no-fallback</buildArg>                                              <!-- 生产环境构建，确保镜像完全脱离JVM依赖 -->
                            <!-- URL和网络配置 -->
                            <buildArg>--enable-url-protocols=http,https</buildArg>                          <!-- 启用HTTP/HTTPS协议 -->
                            <!-- 字符集和错误报告 -->
                            <buildArg>-H:+AddAllCharsets</buildArg>                                         <!-- 添加所有字符集支持 -->
                            <buildArg>-H:+ReportExceptionStackTraces</buildArg>                             <!-- 报告异常堆栈跟踪 -->
                            <!-- Linux特有配置 -->
                            <buildArg>--libc=glibc</buildArg>                                               <!-- 使用glibc -->
                            <buildArg>--verbose</buildArg>                                                  <!-- 详细日志输出 -->
                        </buildArgs>
                    </configuration>
                </plugin>
            </plugins>
        </build>
    </profile>
</profiles>
</project>